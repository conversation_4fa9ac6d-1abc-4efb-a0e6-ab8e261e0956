import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import pinia from './store'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import './style.css'
import VueKonva from 'vue-konva'

async function bootstrap() {
  // 在开发环境下启动 Mock 服务
  if (process.env.NODE_ENV === 'development') {
    const { worker } = await import('./mocks/browser')
    await worker.start()
  }

  const app = createApp(App)

  app.use(router)
  app.use(pinia)
  app.use(ElementPlus)
  app.use(VueKonva)
  app.mount('#app')
}

bootstrap()
