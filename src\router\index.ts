import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import { useAIDetection } from '@/composables/useAIDetection'
import { useCommonStore } from '@/store/common'
import { ElLoading, ElMessage } from 'element-plus'

const routes = [
  {
    path: '/login',
    name: 'login',
    component: () => import('../views/Login.vue')
  },
  {
    path: '/',
    component: Home,
    children: [
      { path: '', redirect: '/import-image' },
      {
        path: 'import-image',
        name: 'import-image',
        component: () => import('../views/ImportImage.vue')
      },
      {
        path: 'face-landmark',
        name: 'face-landmark',
        component: () => import('../views/FaceLandmark.vue')
      },
      { path: 'lip-edit', name: 'lip-edit', component: () => import('../views/LipEdit.vue') },
      {
        path: 'align',
        name: 'align',
        component: () => import('@/views/Align.vue')
      },
      {
        path: 'veneer-adjust',
        name: 'veneer-adjust',
        component: () => import('../views/VeneerAdjust.vue')
      },
      {
        path: 'visual-optimize',
        name: 'visual-optimize',
        component: () => import('../views/VisualOptimize.vue')
      },
      {
        path: 'retouch-tools',
        name: 'retouch-tools',
        component: () => import('../views/RetouchTools.vue')
      },
      {
        path: 'result-share',
        name: 'result-share',
        component: () => import('../views/ResultShare.vue')
      },
      // 测试路由
      { path: 'konva-test', name: 'konva-test', component: () => import('../views/KonvaTest.vue') },
      {
        path: 'spline-curve-demo',
        name: 'spline-curve-demo',
        component: () => import('../views/SplineCurveDemo.vue')
      },
      {
        path: 'texture-test',
        name: 'texture-test',
        component: () => import('../views/TextureTest.vue')
      },
      {
        path: 'veneer-trace',
        name: 'VeneerTrace',
        component: () => import('@/views/VeneerTrace.vue'),
        meta: { hidden: true }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 如果导航到 face-landmark 页面
  if (to.path === '/face-landmark') {
    // 检查是否有照片
    const commonStore = useCommonStore()
    const smileImage = commonStore.smileImage
    const mouthImage = commonStore.mouthImage

    if (smileImage && mouthImage) {
      // 只有在以下情况才获取 AI 检测数据：
      // 1. 从 import-image 页面导航过来，或者直接访问 face-landmark 页面
      // 2. 用户尚未手动调整过关键点
      const isFromImportOrDirect = from.path === '/import-image' || from.path === ''
      const hasManuallyAdjusted = commonStore.hasManuallyAdjustedFacePoints

      if (isFromImportOrDirect && !hasManuallyAdjusted) {
        try {
          // 显示加载中
          const loadingInstance = ElLoading.service({
            lock: true,
            text: '正在分析照片...',
            background: 'rgba(255, 255, 255, 0.7)'
          })

          // 获取 AI 检测数据
          const { fetchAIDetectionData } = useAIDetection()
          await fetchAIDetectionData()

          // 关闭加载
          loadingInstance.close()
        } catch (error) {
          console.error('AI 检测数据获取失败:', error)
          ElMessage.error('照片分析失败，请重试或手动设置点')
          // 即使失败也继续导航，让用户可以手动设置点
        }
      }
    } else {
      // 如果没有照片，提示用户并重定向到导入照片页面
      ElMessage.warning('请先上传微笑照片和开口照片')
      next('/import-image')
      return // 中断当前导航
    }
  }

  // 继续导航
  next()
})

export default router
