/**
 * 曲线相关工具函数
 */

/**
 * 点的接口定义
 */
export interface Point {
  x: number
  y: number
}

/**
 * 计算两点之间的距离
 * @param p1 第一个点
 * @param p2 第二个点
 * @returns 两点之间的欧几里得距离
 */
export function getDistance(p1: Point, p2: Point): number {
  return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2))
}

/**
 * 计算曲线上的点（三次贝塞尔曲线）
 * @param p1 起始点
 * @param p2 结束点
 * @param tension 张力系数
 * @param t 参数t (0-1)
 * @param prevPoint 前一个控制点
 * @param nextPoint 后一个控制点
 * @returns 曲线上的点
 */
export function calculateCurvePoint(
  p1: Point,
  p2: Point,
  tension: number,
  t: number,
  prevPoint?: Point,
  nextPoint?: Point
): Point {
  // 使用张力参数计算曲线上的点
  // 这是一个简化的样条曲线计算，模拟Konva的tension效果
  const p0 = prevPoint || p1
  const p3 = nextPoint || p2

  // 计算控制点
  const cx1 = p1.x + ((p2.x - p0.x) * tension) / 2
  const cy1 = p1.y + ((p2.y - p0.y) * tension) / 2
  const cx2 = p2.x - ((p3.x - p1.x) * tension) / 2
  const cy2 = p2.y - ((p3.y - p1.y) * tension) / 2

  // 计算曲线上的点（三次贝塞尔曲线）
  return {
    x:
      Math.pow(1 - t, 3) * p1.x +
      3 * Math.pow(1 - t, 2) * t * cx1 +
      3 * (1 - t) * Math.pow(t, 2) * cx2 +
      Math.pow(t, 3) * p2.x,
    y:
      Math.pow(1 - t, 3) * p1.y +
      3 * Math.pow(1 - t, 2) * t * cy1 +
      3 * (1 - t) * Math.pow(t, 2) * cy2 +
      Math.pow(t, 3) * p2.y
  }
}

/**
 * 查找曲线上最接近点击位置的点
 * @param clickPoint 点击位置
 * @param points 曲线控制点数组
 * @param tension 张力系数
 * @param threshold 检测阈值
 * @param isClosed 曲线是否闭合
 * @returns 插入点信息，包括索引和坐标
 */
export function findCurveInsertionPoint(
  clickPoint: Point,
  points: Point[],
  tension: number = 0.5,
  threshold: number = 15,
  isClosed: boolean = true
): { index: number; point: Point } | null {
  if (points.length < 2) {
    return null
  }

  let minDistance = Infinity
  let insertIndex = -1
  let insertPoint: Point = { x: 0, y: 0 }

  // 遍历所有线段，并在每个线段上采样多个点来更准确地检测曲线
  for (let i = 0; i < (isClosed ? points.length : points.length - 1); i++) {
    const p1 = points[i]
    if (!p1) {
      continue
    }

    const p2Index = (i + 1) % points.length
    const p2 = points[p2Index] // 闭合曲线，最后一点连接回第一点
    if (!p2) {
      continue
    }

    // 计算线段长度，用于确定采样点数量
    const segmentLength = getDistance(p1, p2)

    // 根据线段长度确定采样点数量，线段越长采样点越多
    const numSamples = Math.max(10, Math.ceil(segmentLength / 10))

    // 在线段上采样多个点
    for (let j = 0; j <= numSamples; j++) {
      const t = j / numSamples

      // 获取前后的控制点，用于计算曲线上的点
      const prevIndex = i === 0 ? (isClosed ? points.length - 1 : 0) : i - 1
      const nextIndex = (i + 2) % points.length

      const prevPoint = points[prevIndex]
      const nextPoint = points[nextIndex]

      // 计算曲线上的点
      const curvePoint = calculateCurvePoint(p1, p2, tension, t, prevPoint || p1, nextPoint || p2)

      // 计算点击位置到曲线上采样点的距离
      const distance = getDistance(clickPoint, curvePoint)

      if (distance < minDistance) {
        minDistance = distance
        insertIndex = i + 1
        insertPoint = curvePoint
      }
    }
  }

  // 如果距离小于阈值，则认为点击在曲线上
  return minDistance < threshold ? { index: insertIndex, point: insertPoint } : null
}

/**
 * 计算曲线上指定X坐标处的Y坐标
 * @param curveNode Konva曲线节点
 * @param targetX 目标X坐标
 * @param tensionFactor 曲线张力因子
 * @returns 对应的Y坐标，如果计算失败则返回null
 */
export function getYAtX(
  curveNode: any,
  targetX: number,
  tensionFactor: number = 0.35
): number | null {
  if (!curveNode || isNaN(targetX)) {
    console.error('getYAtX: 输入 targetX 为 NaN')
    return null
  }

  const cp = curveNode.points() // 获取曲线的原始三个控制点: [xL, yL, xM, yM, xR, yR]
  if (!cp || cp.length < 6) return null

  const P_L = { x: cp[0], y: cp[1] } // 左端点
  const P_M = { x: cp[2], y: cp[3] } // 中间点
  const P_R = { x: cp[4], y: cp[5] } // 右端点

  // 1. 处理 targetX 正好是曲线端点的情况 (使用小容差处理浮点数比较)
  if (Math.abs(targetX - P_L.x) < 1e-6) return P_L.y
  if (Math.abs(targetX - P_R.x) < 1e-6) return P_R.y

  // 2. 确定 targetX 所在的曲线段 (左半段或右半段)
  let p0, p1, p2, p3 // Catmull-Rom 样条所需的四个控制点
  let segmentStartX, segmentEndX // 当前计算段的起始 X 和结束 X

  if (targetX < P_M.x) {
    // targetX 在左半段 (从 P_L 到 P_M)
    p0 = P_L // 对于开放端点，第一个 Catmull-Rom 控制点 p0 通常等于 p1
    p1 = P_L
    p2 = P_M
    p3 = P_R // 第四个控制点 p3 用于影响 p1到p2段的曲率
    segmentStartX = P_L.x
    segmentEndX = P_M.x
  } else {
    // targetX 在右半段 (从 P_M 到 P_R)
    p0 = P_L
    p1 = P_M
    p2 = P_R
    p3 = P_R // 对于开放端点，最后一个 Catmull-Rom 控制点 p3 通常等于 p2
    segmentStartX = P_M.x
    segmentEndX = P_R.x
  }

  // 如果 targetX 严格在当前计算段之外 (理论上已被前面的端点检查覆盖，但作为保险)
  if (targetX < segmentStartX - 1e-6 || targetX > segmentEndX + 1e-6) {
    console.warn(
      `getYAtX: targetX ${targetX} 超出计算段 [${segmentStartX}, ${segmentEndX}]。返回最近端点Y。`
    )
    // 根据 targetX 更接近哪个半段的端点来返回 Y 值
    return Math.abs(targetX - P_L.x) < Math.abs(targetX - P_R.x) ? P_L.y : P_R.y
  }

  // 3. 将 Catmull-Rom 段的控制点转换为等效的三次贝塞尔曲线的两个内部控制点 C1, C2
  const tFactor = tensionFactor // 使用配置的张力因子 (Konva 的 tension)
  const C1 = {
    x: p1.x + ((p2.x - p0.x) * tFactor) / 2,
    y: p1.y + ((p2.y - p0.y) * tFactor) / 2
  }
  const C2 = {
    x: p2.x - ((p3.x - p1.x) * tFactor) / 2,
    y: p2.y - ((p3.y - p1.y) * tFactor) / 2
  }

  // 4. 在此贝塞尔曲线 (由 p1, C1, C2, p2 定义) 上进行采样，找到与 targetX 最匹配的 Y 值
  const numSamples = 50 // 采样点数量，可调整以平衡精度和性能
  let closestXDiff = Infinity // 记录采样点 X 与 targetX 的最小差值
  let bestY = p1.y // 最佳 Y 值的初始猜测 (默认为段起点 Y)
  let foundInterpolationRange = false // 标记是否找到了可以进行线性插值的采样点区间

  const samples = [] // 存储所有采样点 {x, y}

  for (let i = 0; i <= numSamples; i++) {
    const u = i / numSamples // 贝塞尔曲线的参数 t (从 0 到 1)
    const u1 = 1 - u
    // 三次贝塞尔曲线的基函数
    const B0 = u1 * u1 * u1 // (1-u)^3
    const B1 = 3 * u1 * u1 * u // 3*(1-u)^2*u
    const B2 = 3 * u1 * u * u // 3*(1-u)*u^2
    const B3 = u * u * u // u^3

    // 计算采样点的 X 和 Y 坐标
    const x_sample = B0 * p1.x + B1 * C1.x + B2 * C2.x + B3 * p2.x
    const y_sample = B0 * p1.y + B1 * C1.y + B2 * C2.y + B3 * p2.y
    samples.push({ x: x_sample, y: y_sample })
  }

  // 遍历采样点，查找包含 targetX 的区间并进行线性插值
  for (let i = 0; i < samples.length - 1; i++) {
    const s1 = samples[i]
    const s2 = samples[i + 1]

    // 确保s1和s2存在
    if (!s1 || !s2) continue

    // 检查 targetX 是否在当前采样点 s1 和 s2 的 X 坐标之间 (允许 s1.x > s2.x 的情况)
    if (
      (targetX >= s1.x - 1e-6 && targetX <= s2.x + 1e-6) ||
      (targetX >= s2.x - 1e-6 && targetX <= s1.x + 1e-6)
    ) {
      if (Math.abs(s2.x - s1.x) < 1e-6) {
        // 如果两个采样点的 x 几乎相同 (例如垂直部分)
        bestY = (s1.y + s2.y) / 2 // 取两者 Y 的平均值
      } else {
        const ratio = (targetX - s1.x) / (s2.x - s1.x) // 计算插值比例
        bestY = s1.y + ratio * (s2.y - s1.y) // 线性插值得到 Y
      }
      foundInterpolationRange = true
      break // 找到合适的区间后即可跳出循环
    }
  }

  // 如果没有找到可以进行精确线性插值的区间 (例如 targetX 恰好是某个采样点的 X，或者在采样点之外一点点)
  // 则选择所有采样点中 X 值与 targetX 最接近的那个采样点的 Y 值
  if (!foundInterpolationRange) {
    samples.forEach((s) => {
      const diff = Math.abs(s.x - targetX)
      if (diff < closestXDiff) {
        closestXDiff = diff
        bestY = s.y
      }
    })
  }
  return bestY
}
