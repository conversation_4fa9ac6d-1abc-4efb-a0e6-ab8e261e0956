import { InjectionKey, Ref, provide, inject } from 'vue'
import type { TransformParams } from '@/utils/transform2d'

/**
 * 编辑模式枚举
 */
export enum EditMode {
  TRANSFORM = 'transform', // 变换模式
  DETAIL = 'detail', // 精细编辑模式
  MIRROR = 'mirror' // 镜像拷贝模式
}

/**
 * 贴面编辑器上下文接口定义
 * 包含画布渲染、交互以及编辑模式相关的所有状态和方法
 */
export interface VeneerEditorContext {
  // ===== 画布渲染相关状态 =====

  // 画布尺寸
  stageWidth: Ref<number>
  stageHeight: Ref<number>

  // 图片对象
  smileImgObj: Ref<HTMLImageElement | null>
  mouthImgObj: Ref<HTMLImageElement | null>

  // 图片原始尺寸
  smileImgNaturalWidth: Ref<number>
  smileImgNaturalHeight: Ref<number>
  mouthImgNaturalWidth: Ref<number>
  mouthImgNaturalHeight: Ref<number>

  // 图片变换参数
  imgScale: Ref<number>
  imgCenter: Ref<{ x: number; y: number }>
  faceRotation: Ref<number>

  // 布局相关
  headerHeight: Ref<number>

  // 开口照相关
  mouthOpacity: Ref<number>
  sliderValue: Ref<number>
  mouthRenderParams: Ref<{
    x: number
    y: number
    scale: number
    angle: number
  } | null>

  // ===== 编辑模式相关状态 =====

  // 当前编辑模式
  activeTransform: Ref<EditMode>

  // 显示选项
  showMidline: Ref<boolean>
  showSmileFrame: Ref<boolean>

  // 编辑选项
  mirrorEdit: Ref<boolean>
  bridgeEdit: Ref<boolean>

  // ===== 画布交互方法 =====

  getTransformParams: () => TransformParams
  autoFocusToTeethArea: () => void
  handleWheel: (e: WheelEvent) => void
  handleMouseDown: (e: MouseEvent) => void
  handleMouseMove: (e: MouseEvent) => void
  handleMouseUp: (e: MouseEvent) => void
  updateStageSize: (container: HTMLElement | null) => void
  resetInteraction: () => void

  // ===== 编辑模式方法 =====

  // 设置当前编辑模式
  setActiveTransform: (mode: EditMode) => void

  // 获取当前编辑模式的标题
  getTransformTitle: () => string
}

// 创建注入键
export const VeneerEditorKey: InjectionKey<VeneerEditorContext> = Symbol('VeneerEditorContext')

/**
 * 提供贴面编辑器上下文
 * @param context 贴面编辑器上下文对象
 */
export function provideVeneerEditorContext(context: VeneerEditorContext) {
  provide(VeneerEditorKey, context)
}

/**
 * 注入并使用贴面编辑器上下文
 * @returns 贴面编辑器上下文对象
 */
export function useVeneerEditorContext(): VeneerEditorContext {
  const context = inject(VeneerEditorKey)
  if (!context) {
    throw new Error('useVeneerEditorContext must be used within a provider')
  }
  return context
}
