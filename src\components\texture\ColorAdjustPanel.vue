<template>
  <div class="panel-card">
    <div class="panel-header">色彩调整</div>

    <!-- 基础牙齿颜色 -->
    <div class="color-selector">
      <div class="slider-label">基础色</div>
      <el-color-picker
        v-model="baseToothColor"
        size="small"
        show-alpha
        :predefine="predefineColors"
        popper-class="tooth-color-picker"
      />
    </div>

    <!-- 色相调整 -->
    <div class="slider-container hue-slider">
      <div class="slider-label">色相</div>
      <el-slider
        v-model="hueValue"
        :min="-180"
        :max="180"
        :step="1"
        :format-tooltip="(val: number) => `${val}°`"
      />
    </div>

    <!-- 饱和度调整 -->
    <div class="slider-container">
      <div class="slider-label">饱和度</div>
      <el-slider v-model="saturationValue" :min="-1" :max="1" :step="0.01" />
    </div>

    <!-- 亮度调整 -->
    <div class="slider-container">
      <div class="slider-label">亮度</div>
      <el-slider
        v-model="brightnessValue"
        :min="-0.3"
        :max="0.2"
        :step="0.005"
        :show-tooltip="false"
      />
    </div>

    <!-- 纹理强度 -->
    <div class="slider-container">
      <div class="slider-label">纹理强度</div>
      <el-slider v-model="textureStrengthValue" :min="0" :max="1" :step="0.01" />
    </div>

    <!-- 重置按钮 -->
    <div class="reset-button-container">
      <el-button size="small" @click="resetValues">重置色彩</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'

// 预定义牙齿颜色
const predefineColors = [
  '#FFFAF0', // 天然象牙白
  '#F8F6F0', // 自然牙色
  '#F5F2E8', // 稍淡牙色
  '#F0E6D2', // 淡黄牙色
  '#E6D8C8', // 轻度黄染牙色
  '#D8C7B4', // 中度黄染牙色
  '#C8B6A3' // 重度黄染牙色
]

// Props
const props = defineProps({
  hue: {
    type: Number,
    default: 0
  },
  saturation: {
    type: Number,
    default: 0
  },
  brightness: {
    type: Number,
    default: 0
  },
  textureStrength: {
    type: Number,
    default: 0.7
  },
  baseColor: {
    type: String,
    default: '#FFFAF0' // 默认象牙白色
  }
})

// Emits
const emit = defineEmits([
  'update:hue',
  'update:saturation',
  'update:brightness',
  'update:textureStrength',
  'update:baseColor'
])

// 计算属性 - 用于v-model双向绑定
const hueValue = computed({
  get: () => props.hue,
  set: (value) => emit('update:hue', value)
})

const saturationValue = computed({
  get: () => props.saturation,
  set: (value) => emit('update:saturation', value)
})

const brightnessValue = computed({
  get: () => props.brightness,
  set: (value) => emit('update:brightness', value)
})

const textureStrengthValue = computed({
  get: () => props.textureStrength,
  set: (value) => emit('update:textureStrength', value)
})

const baseToothColor = computed({
  get: () => props.baseColor,
  set: (value) => emit('update:baseColor', value)
})

// 重置所有值
function resetValues() {
  emit('update:hue', 0)
  emit('update:saturation', 0)
  emit('update:brightness', 0)
  emit('update:textureStrength', 0.7)
  emit('update:baseColor', '#FFFAF0')
}
</script>

<style scoped>
.panel-card {
  background: white;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.panel-header {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 6px;
  color: #333;
}

.slider-container {
  margin-bottom: 10px;
}

.slider-label {
  font-size: 14px;
  margin-bottom: 6px;
}

.color-selector {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.reset-button-container {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

:deep(.el-checkbox) {
  height: 20px;
}

:deep(.el-slider__runway) {
  margin: 0;
  flex: 1;
}

:deep(.el-slider__bar) {
  border-radius: 3px;
  height: 6px;
  background-color: transparent;
}

:deep(.el-slider__runway) {
  height: 6px;
  background: linear-gradient(to right, #797878, #e6e6e6);
}

:deep(.el-slider__button) {
  border-color: #0052d9;
  box-shadow: 0 0 2px rgba(0, 82, 217, 0.5);
  width: 14px;
  height: 14px;
}

/* 只针对色相滑块轨道设置与Konva一致的蓝色起止渐变 */
.hue-slider :deep(.el-slider__runway) {
  background: linear-gradient(
    to right,
    #0000ff 0%,
    /* -180 蓝 */ #00ffff 16%,
    /* -120 青 */ #00ff00 33%,
    /* -60  绿 */ #ff0000 50%,
    /* 0    红 */ #ffff00 66%,
    /* 60   黄 */ #ff00ff 83%,
    /* 120  紫 */ #0000ff 100% /* 180  蓝 */
  ) !important;
}
</style>
