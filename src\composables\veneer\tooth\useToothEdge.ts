import { ref, Ref, computed, ComputedRef } from 'vue'
import type { KonvaEventObject } from 'konva/lib/Node'
import { getDistance } from '@/utils/curveUtils'
import type { Point } from '@/utils/curveUtils'

/**
 * 获取相邻牙齿的ID
 * @param toothId 当前牙齿ID
 * @returns 相邻牙齿的ID数组 [左相邻, 右相邻]，不存在的相邻牙齿为null
 */
function getAdjacentToothIds(toothId: number): [number | null, number | null] {
  let leftAdjacent: number | null = null
  let rightAdjacent: number | null = null

  if (toothId > 0) {
    // 正数牙齿（右侧）
    if (toothId === 1) {
      leftAdjacent = -1 // 1号牙齿的左相邻是-1号
      rightAdjacent = 2 // 1号牙齿的右相邻是2号
    } else if (toothId < 5) {
      leftAdjacent = toothId - 1
      rightAdjacent = toothId + 1
    } else if (toothId === 5) {
      leftAdjacent = 4
      rightAdjacent = null // 5号牙齿没有右相邻
    }
  } else if (toothId < 0) {
    // 负数牙齿（左侧）
    if (toothId === -1) {
      leftAdjacent = -2 // -1号牙齿的左相邻是-2号
      rightAdjacent = 1 // -1号牙齿的右相邻是1号
    } else if (toothId > -5) {
      leftAdjacent = toothId - 1
      rightAdjacent = toothId + 1
    } else if (toothId === -5) {
      leftAdjacent = null // -5号牙齿没有左相邻
      rightAdjacent = -4
    }
  }

  return [leftAdjacent, rightAdjacent]
}

/**
 * 检查两个牙齿是否相邻
 * @param toothId1 第一个牙齿ID
 * @param toothId2 第二个牙齿ID
 * @returns 是否相邻
 */
function areTeethAdjacent(toothId1: number, toothId2: number): boolean {
  const [leftAdjacent, rightAdjacent] = getAdjacentToothIds(toothId1)
  return toothId2 === leftAdjacent || toothId2 === rightAdjacent
}

/**
 * 处理牙齿边缘拖拽的 composable
 * 提供牙齿边缘拖拽相关的状态和方法
 *
 * @param controlPoints 牙齿控制点的响应式引用
 * @param segmentInfo 区块信息的响应式引用
 * @param isTransformMode 是否处于变换模式的计算属性
 * @param isRotating 是否正在旋转的响应式引用
 * @param toothId 牙齿ID的响应式引用
 * @param mirrorEdit 是否启用镜像编辑的响应式引用
 * @param bridgeEdit 是否启用连桥编辑的响应式引用
 * @param updateToothShape 更新牙齿形状的回调函数
 * @param getCurrentToothShape 获取当前牙齿形状的函数
 * @param getMirrorToothId 获取镜像牙齿ID的函数
 * @param getSegmentById 根据ID获取区块信息的函数
 * @returns 边缘拖拽相关的状态和方法
 */
export function useToothEdge(
  controlPoints: Ref<Point[]>,
  segmentInfo: Ref<any>,
  isTransformMode: ComputedRef<boolean>,
  isRotating: Ref<boolean>,
  toothId: Ref<number>,
  mirrorEdit: Ref<boolean>,
  bridgeEdit: Ref<boolean>,
  updateToothShape: (id: number, shape: any) => void,
  getCurrentToothShape: (id: number) => any,
  getMirrorToothId: (id: number) => number | null,
  getSegmentById: (id: number) => any | undefined
) {
  // 边缘拖拽相关变量
  const edgeDetectionThreshold = 15 // 边缘检测阈值（像素）
  const isEdgeDragging = ref(false) // 是否正在拖拽边缘
  const currentEdgeType = ref<'top' | 'bottom' | 'left' | 'right' | null>(null) // 当前拖拽的边缘类型
  const initialMousePosition = ref<Point>({ x: 0, y: 0 }) // 拖拽开始时的鼠标位置
  const initialBoundingBox = ref<any>(null) // 拖拽开始时的包围盒
  const initialPoints = ref<Point[]>([]) // 拖拽开始时的控制点
  const mirrorInitialPoints = ref<Point[]>([]) // 镜像牙齿的初始控制点
  const edgeDragJustEnded = ref(false) // 标记边缘拖拽是否刚刚结束

  // 连桥编辑相关变量
  const isBridgeDragging = ref(false) // 是否正在进行连桥拖拽
  const bridgeAdjacentToothId = ref<number | null>(null) // 连桥编辑中的相邻牙齿ID
  const adjacentInitialPoints = ref<Point[]>([]) // 相邻牙齿的初始控制点
  const adjacentInitialBoundingBox = ref<any>(null) // 相邻牙齿的初始包围盒

  // 用于优化性能的变量
  let dragUpdateScheduled = false

  // 计算牙齿的包围盒
  const toothBoundingBox = computed(() => {
    if (controlPoints.value.length === 0) {
      return {
        minX: 0,
        maxX: 0,
        minY: 0,
        maxY: 0,
        width: 0,
        height: 0,
        centerX: 0,
        centerY: 0
      }
    }

    const xs = controlPoints.value.map((p) => p.x)
    const ys = controlPoints.value.map((p) => p.y)

    const minX = Math.min(...xs)
    const maxX = Math.max(...xs)
    const minY = Math.min(...ys)
    const maxY = Math.max(...ys)
    const width = maxX - minX
    const height = maxY - minY
    const centerX = (minX + maxX) / 2
    const centerY = (minY + maxY) / 2

    return {
      minX,
      maxX,
      minY,
      maxY,
      width,
      height,
      centerX,
      centerY
    }
  })

  /**
   * 检测鼠标是否在牙齿边缘上，并返回边缘类型和连桥编辑信息
   */
  function detectEdge(point: Point): {
    edgeType: 'top' | 'bottom' | 'left' | 'right' | null
    isBridgeEdge: boolean
    adjacentToothId: number | null
  } {
    if (!isTransformMode.value) {
      return { edgeType: null, isBridgeEdge: false, adjacentToothId: null }
    }

    const box = toothBoundingBox.value
    const { minX, maxX, minY, maxY, centerX } = box

    // 检查点是否在牙齿轮廓附近
    if (
      point.x < minX - edgeDetectionThreshold ||
      point.x > maxX + edgeDetectionThreshold ||
      point.y < minY - edgeDetectionThreshold ||
      point.y > maxY + edgeDetectionThreshold
    ) {
      return { edgeType: null, isBridgeEdge: false, adjacentToothId: null }
    }

    // 使用对角线划分区域
    const distToTopLeft = getDistance(point, { x: minX, y: minY })
    const distToTopRight = getDistance(point, { x: maxX, y: minY })
    const distToBottomLeft = getDistance(point, { x: minX, y: maxY })
    const distToBottomRight = getDistance(point, { x: maxX, y: maxY })

    // 找到最近的角点
    const minDist = Math.min(distToTopLeft, distToTopRight, distToBottomLeft, distToBottomRight)

    let edgeType: 'top' | 'bottom' | 'left' | 'right' | null = null

    // 根据最近的角点和鼠标位置判断边缘
    if (minDist === distToTopLeft || minDist === distToTopRight) {
      // 上半部分
      if (point.x < centerX && Math.abs(point.x - minX) < edgeDetectionThreshold) {
        edgeType = 'left'
      } else if (point.x > centerX && Math.abs(point.x - maxX) < edgeDetectionThreshold) {
        edgeType = 'right'
      } else if (Math.abs(point.y - minY) < edgeDetectionThreshold) {
        edgeType = 'top'
      }
    } else {
      // 下半部分
      if (point.x < centerX && Math.abs(point.x - minX) < edgeDetectionThreshold) {
        edgeType = 'left'
      } else if (point.x > centerX && Math.abs(point.x - maxX) < edgeDetectionThreshold) {
        edgeType = 'right'
      } else if (Math.abs(point.y - maxY) < edgeDetectionThreshold) {
        edgeType = 'bottom'
      }
    }

    // 检查是否为连桥编辑
    let isBridgeEdge = false
    let adjacentToothId: number | null = null

    if (bridgeEdit.value && (edgeType === 'left' || edgeType === 'right')) {
      const [leftAdjacent, rightAdjacent] = getAdjacentToothIds(toothId.value)

      if (edgeType === 'left' && leftAdjacent !== null) {
        // 检查左相邻牙齿是否存在
        const leftSegment = getSegmentById(leftAdjacent)
        if (leftSegment) {
          isBridgeEdge = true
          adjacentToothId = leftAdjacent
        }
      } else if (edgeType === 'right' && rightAdjacent !== null) {
        // 检查右相邻牙齿是否存在
        const rightSegment = getSegmentById(rightAdjacent)
        if (rightSegment) {
          isBridgeEdge = true
          adjacentToothId = rightAdjacent
        }
      }
    }

    return { edgeType, isBridgeEdge, adjacentToothId }
  }

  /**
   * 处理边缘拖拽开始
   */
  function handleEdgeDragStart(
    mousePos: Point,
    edgeType: 'top' | 'bottom' | 'left' | 'right',
    stage: any,
    isBridgeEdge: boolean = false,
    adjacentToothId: number | null = null
  ) {
    // 只在变换模式下允许边缘拖拽
    if (!isTransformMode.value) {
      return
    }

    // 标记为正在拖拽边缘
    isEdgeDragging.value = true

    // 记录当前拖拽的边缘类型
    currentEdgeType.value = edgeType

    // 记录拖拽开始时的鼠标位置
    initialMousePosition.value = { ...mousePos }

    // 记录拖拽开始时的包围盒
    initialBoundingBox.value = { ...toothBoundingBox.value }

    // 记录初始控制点
    initialPoints.value = [...controlPoints.value]

    // 连桥编辑相关初始化
    if (isBridgeEdge && adjacentToothId !== null) {
      isBridgeDragging.value = true
      bridgeAdjacentToothId.value = adjacentToothId

      // 获取相邻牙齿的区块信息
      const adjacentSegment = getSegmentById(adjacentToothId)
      if (adjacentSegment) {
        // 获取相邻牙齿的当前形状
        const adjacentShape = getCurrentToothShape(adjacentToothId)
        if (adjacentShape && adjacentShape.points && adjacentShape.points.length > 0) {
          // 计算相邻牙齿的绝对坐标控制点
          const adjacentCenterX = adjacentSegment.startX + adjacentSegment.width / 2
          const adjacentCenterY = adjacentSegment.topY + adjacentSegment.height / 2

          const adjacentAbsolutePoints = adjacentShape.points.map((point: Point) => ({
            x: adjacentCenterX + (point.x * adjacentSegment.width) / 2,
            y: adjacentCenterY + (point.y * adjacentSegment.height) / 2
          }))

          // 保存相邻牙齿的初始控制点
          adjacentInitialPoints.value = [...adjacentAbsolutePoints]

          // 计算相邻牙齿的包围盒
          const adjacentXs = adjacentAbsolutePoints.map((p: Point) => p.x)
          const adjacentYs = adjacentAbsolutePoints.map((p: Point) => p.y)
          const adjacentMinX = Math.min(...adjacentXs)
          const adjacentMaxX = Math.max(...adjacentXs)
          const adjacentMinY = Math.min(...adjacentYs)
          const adjacentMaxY = Math.max(...adjacentYs)

          adjacentInitialBoundingBox.value = {
            minX: adjacentMinX,
            maxX: adjacentMaxX,
            minY: adjacentMinY,
            maxY: adjacentMaxY,
            width: adjacentMaxX - adjacentMinX,
            height: adjacentMaxY - adjacentMinY,
            centerX: (adjacentMinX + adjacentMaxX) / 2,
            centerY: (adjacentMinY + adjacentMaxY) / 2
          }
        }
      }
    } else {
      isBridgeDragging.value = false
      bridgeAdjacentToothId.value = null
      adjacentInitialPoints.value = []
      adjacentInitialBoundingBox.value = null
    }

    // 记录镜像牙齿的初始控制点（如果有的话）
    if (mirrorEdit.value) {
      const mirrorId = getMirrorToothId(toothId.value)
      if (mirrorId !== null) {
        // 获取镜像牙齿的区块信息
        const mirrorSegment = getSegmentById(mirrorId)

        if (mirrorSegment) {
          // 获取镜像牙齿当前的形状
          const mirrorShape = getCurrentToothShape(mirrorId)

          if (mirrorShape && mirrorShape.points && mirrorShape.points.length > 0) {
            // 保存镜像牙齿的初始控制点到响应式变量
            mirrorInitialPoints.value = [...mirrorShape.points]
          }
        }
      }
    }

    // 设置鼠标样式
    if (edgeType === 'top' || edgeType === 'bottom') {
      document.body.style.cursor = 'ns-resize'
    } else {
      document.body.style.cursor = 'ew-resize'
    }

    // 在舞台上添加一次性的事件监听器，以便在鼠标离开牙齿轮廓时也能继续拖拽
    const handleStageDrag = () => {
      const pos = stage.getPointerPosition()
      if (pos) {
        handleEdgeDrag({ x: pos.x, y: pos.y })
      }
    }

    const handleStageUp = () => {
      // 移除事件监听器
      stage.off('mousemove', handleStageDrag)
      stage.off('mouseup', handleStageUp)
      stage.off('touchmove', handleStageDrag)
      stage.off('touchend', handleStageUp)

      // 结束拖拽
      handleEdgeEnd()

      // 设置一个标志，表示这是边缘拖拽结束
      // 这个标志将在下一个事件循环中被检查，以防止选中牙齿
      edgeDragJustEnded.value = true

      // 100ms后重置标志
      setTimeout(() => {
        edgeDragJustEnded.value = false
      }, 100)
    }

    // 添加事件监听器
    stage.on('mousemove', handleStageDrag)
    stage.on('mouseup', handleStageUp)
    stage.on('touchmove', handleStageDrag)
    stage.on('touchend', handleStageUp)
  }

  /**
   * 处理边缘拖拽
   */
  function handleEdgeDrag(mousePos: Point) {
    // 只在变换模式下允许边缘拖拽
    if (
      !isTransformMode.value ||
      !isEdgeDragging.value ||
      !currentEdgeType.value ||
      !initialBoundingBox.value
    ) {
      return
    }

    // 计算鼠标移动的距离
    const deltaX = mousePos.x - initialMousePosition.value.x
    const deltaY = mousePos.y - initialMousePosition.value.y

    // 根据边缘类型计算缩放因子
    let scaleX = 1
    let scaleY = 1
    let translateX = 0
    let translateY = 0

    const box = initialBoundingBox.value
    const { width, height, centerX, centerY } = box

    // 根据边缘类型计算缩放和平移
    switch (currentEdgeType.value) {
      case 'left':
        // 只允许水平方向拖拽
        if (width - deltaX <= 0) return // 防止宽度变为负数
        scaleX = (width - deltaX) / width
        translateX = deltaX / 2 // 向右拖动左边缘时，整体向右移动一半的距离
        break
      case 'right':
        // 只允许水平方向拖拽
        if (width + deltaX <= 0) return // 防止宽度变为负数
        scaleX = (width + deltaX) / width
        translateX = deltaX / 2 // 向右拖动右边缘时，整体向右移动一半的距离
        break
      case 'top':
        // 只允许垂直方向拖拽
        if (height - deltaY <= 0) return // 防止高度变为负数
        scaleY = (height - deltaY) / height
        translateY = deltaY / 2 // 向下拖动上边缘时，整体向下移动一半的距离
        break
      case 'bottom':
        // 只允许垂直方向拖拽
        if (height + deltaY <= 0) return // 防止高度变为负数
        scaleY = (height + deltaY) / height
        translateY = deltaY / 2 // 向下拖动下边缘时，整体向下移动一半的距离
        break
    }

    // 使用requestAnimationFrame优化性能
    if (!dragUpdateScheduled) {
      dragUpdateScheduled = true
      requestAnimationFrame(() => {
        // 应用变换到所有控制点
        const newPoints = initialPoints.value.map((point) => {
          // 计算点相对于中心的位置
          const relX = point.x - centerX
          const relY = point.y - centerY

          // 应用缩放
          const scaledX = relX * scaleX
          const scaledY = relY * scaleY

          // 应用平移并转换回绝对坐标
          return {
            x: centerX + scaledX + translateX,
            y: centerY + scaledY + translateY
          }
        })

        // 更新控制点
        controlPoints.value = newPoints

        // 计算并保存相对形状（相对于中心点的比例）
        updateRelativeShape(newPoints)

        // 镜像编辑：如果启用了镜像编辑，同步更新对应的镜像牙齿
        if (mirrorEdit.value) {
          const mirrorId = getMirrorToothId(toothId.value)
          if (mirrorId !== null) {
            // 获取镜像牙齿的区块信息
            const mirrorSegment = getSegmentById(mirrorId)

            if (mirrorSegment) {
              // 获取镜像牙齿当前的形状
              const mirrorShape = getCurrentToothShape(mirrorId)

              if (mirrorShape && mirrorShape.points && mirrorShape.points.length > 0) {
                // 对于镜像牙齿，我们需要应用相同的变换，但X方向的变换需要镜像
                // 使用与原始牙齿相同的缩放比例
                let mirrorScaleX = scaleX
                let mirrorScaleY = scaleY
                let mirrorTranslateX = translateX
                let mirrorTranslateY = translateY

                // 根据边缘类型调整镜像变换
                if (currentEdgeType.value === 'left' || currentEdgeType.value === 'right') {
                  // 左/右边缘拖拽时，镜像牙齿应该是相反方向
                  mirrorTranslateX = -translateX
                }

                // 获取镜像牙齿的区块中心点
                const mirrorSegmentCenterX = mirrorSegment.startX + mirrorSegment.width / 2
                const mirrorSegmentCenterY = mirrorSegment.topY + mirrorSegment.height / 2

                // 使用保存的镜像牙齿初始控制点作为基础（如果有的话）
                // 这样可以避免累积误差
                const basePoints =
                  mirrorInitialPoints.value.length > 0
                    ? mirrorInitialPoints.value
                    : mirrorShape.points

                // 计算镜像牙齿的实际中心点（而不是使用区块中心点）
                // 首先将相对坐标转换为绝对坐标
                const mirrorAbsPoints = basePoints.map((point: Point) => {
                  return {
                    x: mirrorSegmentCenterX + point.x * (mirrorSegment.width / 2),
                    y: mirrorSegmentCenterY + point.y * (mirrorSegment.height / 2)
                  }
                })

                // 使用绝对坐标计算镜像牙齿的实际中心点
                const mirrorSumX = mirrorAbsPoints.reduce(
                  (sum: number, point: Point) => sum + point.x,
                  0
                )
                const mirrorSumY = mirrorAbsPoints.reduce(
                  (sum: number, point: Point) => sum + point.y,
                  0
                )
                const mirrorCenterX = mirrorSumX / mirrorAbsPoints.length
                const mirrorCenterY = mirrorSumY / mirrorAbsPoints.length

                // 应用变换到镜像牙齿的每个点
                const mirrorPoints = basePoints.map((point: Point) => {
                  // 将相对坐标转换为绝对坐标
                  const absX = mirrorSegmentCenterX + point.x * (mirrorSegment.width / 2)
                  const absY = mirrorSegmentCenterY + point.y * (mirrorSegment.height / 2)

                  // 计算点相对于实际中心的位置
                  const relX = absX - mirrorCenterX
                  const relY = absY - mirrorCenterY

                  // 应用缩放
                  const scaledX = relX * mirrorScaleX
                  const scaledY = relY * mirrorScaleY

                  // 应用平移并转换回相对坐标
                  const newAbsX = mirrorCenterX + scaledX + mirrorTranslateX
                  const newAbsY = mirrorCenterY + scaledY + mirrorTranslateY

                  return {
                    x: (newAbsX - mirrorSegmentCenterX) / (mirrorSegment.width / 2),
                    y: (newAbsY - mirrorSegmentCenterY) / (mirrorSegment.height / 2)
                  }
                })

                // 更新镜像牙齿的形状
                updateToothShape(mirrorId, {
                  points: mirrorPoints
                })
              }
            }
          }
        }

        // 连桥编辑：如果启用了连桥编辑且正在进行连桥拖拽，同步更新相邻牙齿
        if (
          isBridgeDragging.value &&
          bridgeAdjacentToothId.value !== null &&
          adjacentInitialBoundingBox.value
        ) {
          const adjacentToothId = bridgeAdjacentToothId.value
          const adjacentSegment = getSegmentById(adjacentToothId)

          if (adjacentSegment && adjacentInitialPoints.value.length > 0) {
            const adjacentBox = adjacentInitialBoundingBox.value
            const {
              width: adjWidth,
              height: adjHeight,
              centerX: adjCenterX,
              centerY: adjCenterY
            } = adjacentBox

            // 对相邻牙齿应用相同的变换
            const adjacentNewPoints = adjacentInitialPoints.value.map((point: Point) => {
              // 计算点相对于相邻牙齿中心的位置
              const relX = point.x - adjCenterX
              const relY = point.y - adjCenterY

              // 应用缩放
              const scaledX = relX * scaleX
              const scaledY = relY * scaleY

              // 应用平移并转换回绝对坐标
              return {
                x: adjCenterX + scaledX + translateX,
                y: adjCenterY + scaledY + translateY
              }
            })

            // 将绝对坐标转换为相对坐标并更新相邻牙齿的形状
            const adjacentSegmentCenterX = adjacentSegment.startX + adjacentSegment.width / 2
            const adjacentSegmentCenterY = adjacentSegment.topY + adjacentSegment.height / 2

            const adjacentRelativeShape = adjacentNewPoints.map((point: Point) => ({
              x: (point.x - adjacentSegmentCenterX) / (adjacentSegment.width / 2),
              y: (point.y - adjacentSegmentCenterY) / (adjacentSegment.height / 2)
            }))

            // 更新相邻牙齿的形状
            updateToothShape(adjacentToothId, {
              points: adjacentRelativeShape
            })

            // 如果镜像编辑也启用了，还需要更新相邻牙齿的镜像牙齿
            if (mirrorEdit.value) {
              const adjacentMirrorId = getMirrorToothId(adjacentToothId)
              if (adjacentMirrorId !== null) {
                const adjacentMirrorSegment = getSegmentById(adjacentMirrorId)
                if (adjacentMirrorSegment) {
                  const adjacentMirrorShape = getCurrentToothShape(adjacentMirrorId)
                  if (
                    adjacentMirrorShape &&
                    adjacentMirrorShape.points &&
                    adjacentMirrorShape.points.length > 0
                  ) {
                    // 对相邻牙齿的镜像应用镜像变换
                    let adjMirrorScaleX = scaleX
                    let adjMirrorTranslateX = translateX

                    // 根据边缘类型调整镜像变换
                    if (currentEdgeType.value === 'left' || currentEdgeType.value === 'right') {
                      adjMirrorTranslateX = -translateX
                    }

                    // 获取相邻镜像牙齿的区块中心点
                    const adjMirrorSegmentCenterX =
                      adjacentMirrorSegment.startX + adjacentMirrorSegment.width / 2
                    const adjMirrorSegmentCenterY =
                      adjacentMirrorSegment.topY + adjacentMirrorSegment.height / 2

                    // 计算相邻镜像牙齿的绝对坐标控制点
                    const adjMirrorAbsPoints = adjacentMirrorShape.points.map((point: Point) => ({
                      x: adjMirrorSegmentCenterX + point.x * (adjacentMirrorSegment.width / 2),
                      y: adjMirrorSegmentCenterY + point.y * (adjacentMirrorSegment.height / 2)
                    }))

                    // 计算相邻镜像牙齿的实际中心点
                    const adjMirrorSumX = adjMirrorAbsPoints.reduce(
                      (sum: number, point: Point) => sum + point.x,
                      0
                    )
                    const adjMirrorSumY = adjMirrorAbsPoints.reduce(
                      (sum: number, point: Point) => sum + point.y,
                      0
                    )
                    const adjMirrorCenterX = adjMirrorSumX / adjMirrorAbsPoints.length
                    const adjMirrorCenterY = adjMirrorSumY / adjMirrorAbsPoints.length

                    // 应用变换到相邻镜像牙齿的每个点
                    const adjMirrorPoints = adjacentMirrorShape.points.map((point: Point) => {
                      // 将相对坐标转换为绝对坐标
                      const absX =
                        adjMirrorSegmentCenterX + point.x * (adjacentMirrorSegment.width / 2)
                      const absY =
                        adjMirrorSegmentCenterY + point.y * (adjacentMirrorSegment.height / 2)

                      // 计算点相对于实际中心的位置
                      const relX = absX - adjMirrorCenterX
                      const relY = absY - adjMirrorCenterY

                      // 应用缩放
                      const scaledX = relX * adjMirrorScaleX
                      const scaledY = relY * scaleY

                      // 应用平移并转换回相对坐标
                      const newAbsX = adjMirrorCenterX + scaledX + adjMirrorTranslateX
                      const newAbsY = adjMirrorCenterY + scaledY + translateY

                      return {
                        x: (newAbsX - adjMirrorSegmentCenterX) / (adjacentMirrorSegment.width / 2),
                        y: (newAbsY - adjMirrorSegmentCenterY) / (adjacentMirrorSegment.height / 2)
                      }
                    })

                    // 更新相邻镜像牙齿的形状
                    updateToothShape(adjacentMirrorId, {
                      points: adjMirrorPoints
                    })
                  }
                }
              }
            }
          }
        }

        dragUpdateScheduled = false
      })
    }
  }

  /**
   * 处理边缘拖拽结束
   */
  function handleEdgeEnd() {
    // 只在变换模式下且正在拖拽边缘时才执行
    if (!isTransformMode.value || !isEdgeDragging.value) {
      return
    }

    // 标记拖拽结束
    isEdgeDragging.value = false
    currentEdgeType.value = null

    // 恢复鼠标样式
    document.body.style.cursor = 'default'

    // 清除保存的镜像牙齿初始控制点
    mirrorInitialPoints.value = []

    // 清除连桥编辑相关状态
    isBridgeDragging.value = false
    bridgeAdjacentToothId.value = null
    adjacentInitialPoints.value = []
    adjacentInitialBoundingBox.value = null
  }

  /**
   * 更新相对形状并保存到store
   */
  function updateRelativeShape(points: Point[]) {
    // 计算并保存相对形状（相对于中心点的比例）
    const { startX, width, height, topY } = segmentInfo.value
    const segmentCenterX = startX + width / 2
    const segmentCenterY = topY + height / 2

    // 计算每个点相对于中心点的比例位置
    const newRelativeShape = points.map((point) => ({
      x: (point.x - segmentCenterX) / (width / 2),
      y: (point.y - segmentCenterY) / (height / 2)
    }))

    // 更新store中的自定义牙齿形状
    updateToothShape(toothId.value, {
      points: newRelativeShape
    })
  }

  /**
   * 处理鼠标在牙齿轮廓上移动，检测是否在边缘上
   */
  function handleMouseMove(e: KonvaEventObject<MouseEvent>) {
    // 只在变换模式下启用边缘拖拽功能
    if (!isTransformMode.value || isRotating.value) {
      return
    }

    // 获取鼠标位置
    const stage = e.target.getStage()
    if (!stage) return
    const pos = stage.getPointerPosition()
    if (!pos) return

    // 检测鼠标是否在边缘上
    const edgeDetection = detectEdge({ x: pos.x, y: pos.y })
    const { edgeType, isBridgeEdge } = edgeDetection

    // 根据边缘类型设置鼠标样式
    if (edgeType === 'top' || edgeType === 'bottom') {
      document.body.style.cursor = 'ns-resize'
    } else if (edgeType === 'left' || edgeType === 'right') {
      // 如果是连桥编辑，使用特殊的鼠标样式
      if (isBridgeEdge) {
        document.body.style.cursor = 'col-resize' // 或者其他表示连桥编辑的样式
      } else {
        document.body.style.cursor = 'ew-resize'
      }
    } else {
      // 如果不在边缘上，恢复默认鼠标样式
      if (document.body.style.cursor !== 'move') {
        document.body.style.cursor = 'default'
      }
    }
  }

  /**
   * 处理鼠标按下事件，检测是否在边缘上并开始拖拽
   */
  function handleMouseDown(e: KonvaEventObject<MouseEvent>) {
    // 只在变换模式下启用边缘拖拽功能
    if (!isTransformMode.value || isRotating.value) {
      return
    }

    // 获取鼠标位置
    const stage = e.target.getStage()
    if (!stage) return
    const pos = stage.getPointerPosition()
    if (!pos) return

    // 检测鼠标是否在边缘上
    const edgeDetection = detectEdge({ x: pos.x, y: pos.y })
    const { edgeType, isBridgeEdge, adjacentToothId } = edgeDetection

    // 如果在边缘上，开始边缘拖拽
    if (edgeType) {
      // 阻止事件冒泡，防止触发其他事件
      e.cancelBubble = true

      // 开始边缘拖拽（传递连桥编辑信息）
      handleEdgeDragStart({ x: pos.x, y: pos.y }, edgeType, stage, isBridgeEdge, adjacentToothId)
      return true
    }

    return false
  }

  return {
    isEdgeDragging,
    currentEdgeType,
    edgeDragJustEnded,
    initialPoints,
    toothBoundingBox,
    // 连桥编辑相关状态
    isBridgeDragging,
    bridgeAdjacentToothId,
    // 方法
    detectEdge,
    handleEdgeDragStart,
    handleEdgeDrag,
    handleEdgeEnd,
    handleMouseMove,
    handleMouseDown
  }
}
