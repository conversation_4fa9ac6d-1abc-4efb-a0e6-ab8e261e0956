import { ref, computed, Ref } from 'vue'
import { EditMode } from '@/contexts/VeneerEditorContext'
import type { Point } from '@/utils/curveUtils'
import type { SegmentInfo } from '@/store/teeth'
import { ElMessage } from 'element-plus'
import Konva from 'konva'

/**
 * 镜像拷贝模式的组合式函数
 * 处理镜像拷贝模式下的交互和操作
 *
 * @param editorContext 编辑器上下文
 * @param teethStore 牙齿状态存储
 * @param toothId 当前牙齿ID
 * @param lineRef 当前牙齿Konva.Line对象的引用 (之前叫layerRef，更名以明确其含义)
 * @param useFrameCenterlineSymmetry 是否使用框架中线对称（默认false，使用原有的牙齿中心镜像）
 * @param keepVerticalConsistent 是否保持垂直Y坐标一致（默认false，Y坐标与原有的牙齿相同）
 * @returns 镜像拷贝相关的状态和方法
 */
export function useMirrorCopy(
  editorContext: any,
  teethStore: any,
  toothId: Ref<number>,
  lineRef: Ref<any>,
  useFrameCenterlineSymmetry: Ref<boolean> = ref(false), // 是否使用框架中线对称
  keepVerticalConsistent: Ref<boolean> = ref(false) // 是否保持垂直Y坐标一致
) {
  const getToothControlPoints = (id: number) => {
    const shape = teethStore.getCurrentToothShape(id)
    if (!shape || !shape.points || shape.points.length === 0) return null

    const segment = teethStore.segments.find((s: SegmentInfo) => s.index === id)
    if (!segment) return null

    const centerX = segment.startX + segment.width / 2
    const centerY = segment.topY + segment.height / 2

    return shape.points.map((point: Point) => ({
      x: centerX + (point.x * segment.width) / 2,
      y: centerY + (point.y * segment.height) / 2
    }))
  }

  const getToothCenter = (id: number) => {
    const controlPoints = getToothControlPoints(id)
    if (!controlPoints || controlPoints.length === 0) {
      const segment = teethStore.segments.find((s: SegmentInfo) => s.index === id)
      if (!segment) return null
      return {
        x: segment.startX + segment.width / 2,
        y: segment.topY + segment.height / 2
      }
    }
    const sumX = controlPoints.reduce((sum: number, point: Point) => sum + point.x, 0)
    const sumY = controlPoints.reduce((sum: number, point: Point) => sum + point.y, 0)
    return {
      x: sumX / controlPoints.length,
      y: sumY / controlPoints.length
    }
  }

  const isMirrorMode = computed(() => editorContext.activeTransform.value === EditMode.MIRROR)
  const showMirrorPreview = ref(false)
  const mirrorArrowRef = ref<Konva.Arrow | null>(null) // Typed for clarity
  const mirrorPreviewRef = ref<Konva.Line | null>(null) // Typed for clarity
  const targetToothId = computed(() => getMirrorToothId(toothId.value))

  const targetSegmentInfo = computed(() => {
    if (targetToothId.value === null) return null
    return teethStore.segments.find((segment: SegmentInfo) => segment.index === targetToothId.value)
  })

  function getMirrorToothId(id: number): number | null {
    const mirrorId = -id
    const exists = teethStore.segments.some((segment: SegmentInfo) => segment.index === mirrorId)
    return exists ? mirrorId : null
  }

  /**
   * 获取用于绘制镜像指示（箭头、预览）的Konva图层。
   * @param currentLineNode 当前牙齿的 Konva.Line 节点。
   * @returns Konva.Layer | null
   */
  function getDrawingLayer(currentLineNode: Konva.Node | null): Konva.Layer | null {
    if (!currentLineNode) {
      console.error('[useMirrorCopy] Current tooth line node is not available.')
      return null
    }
    const drawingLayer = currentLineNode.getLayer() as Konva.Layer | null
    if (drawingLayer) {
      return drawingLayer
    } else {
      return null
    }
  }

  function showArrow() {
    if (!isMirrorMode.value || targetToothId.value === null) return

    const sourceCenter = getToothCenter(toothId.value)
    const targetCenter = getToothCenter(targetToothId.value)

    if (!sourceCenter || !targetCenter) return

    hideArrow() // 清除现有箭头

    const currentToothNode = lineRef.value?.getNode?.()
    const drawingLayer = getDrawingLayer(currentToothNode)

    if (!drawingLayer) {
      console.error('[useMirrorCopy showArrow] Failed to get a drawing layer.')
      return
    }

    const startX = sourceCenter.x
    const startY = sourceCenter.y

    // 根据镜像模式计算箭头终点X坐标
    let endX: number
    if (useFrameCenterlineSymmetry.value) {
      // 框架中线对称模式：箭头指向源牙齿在中线对称的位置
      const centerline = getFrameCenterline()
      if (centerline !== null) {
        endX = 2 * centerline - sourceCenter.x
      } else {
        // 如果无法获取中线，回退到目标牙齿中心
        endX = targetCenter.x
      }
    } else {
      // 牙齿中心镜像模式：箭头指向目标牙齿中心
      endX = targetCenter.x
    }

    // 根据Y坐标配置计算箭头终点Y坐标
    let endY: number
    if (keepVerticalConsistent.value) {
      // 保持Y坐标一致：箭头水平
      endY = sourceCenter.y
    } else {
      // 不保持Y坐标一致：箭头指向目标牙齿的Y位置
      endY = targetCenter.y
    }

    const arrow = new Konva.Arrow({
      points: [startX, startY, endX, endY],
      pointerLength: 12,
      pointerWidth: 12,
      fill: '#4169e1',
      stroke: '#4169e1',
      strokeWidth: 3,
      dash: [5, 5],
      listening: false,
      name: 'mirror-arrow'
    })

    drawingLayer.add(arrow)
    mirrorArrowRef.value = arrow
    drawingLayer.batchDraw()
  }

  function hideArrow() {
    if (mirrorArrowRef.value) {
      const layer = mirrorArrowRef.value.getLayer()
      mirrorArrowRef.value.destroy()
      mirrorArrowRef.value = null
      layer?.batchDraw()
    }
  }

  function showPreview() {
    if (!isMirrorMode.value || targetToothId.value === null) return

    const sourceShape = teethStore.getCurrentToothShape(toothId.value)
    const targetCenter = getToothCenter(targetToothId.value)

    if (!sourceShape || !targetCenter) return

    hidePreview() // 清除现有预览

    const currentToothNode = lineRef.value?.getNode?.()
    const drawingLayer = getDrawingLayer(currentToothNode)

    if (!drawingLayer) {
      console.error('[useMirrorCopy showPreview] Failed to get a drawing layer.')
      return
    }

    const sourceCenter = getToothCenter(toothId.value)
    if (!sourceCenter) return

    // 获取源牙齿的控制点（绝对坐标）
    const sourceControlPoints = getToothControlPoints(toothId.value)
    if (!sourceControlPoints) return

    const previewPoints = sourceControlPoints.map((point: Point) => {
      return calculateMirroredPoint(point, sourceCenter, targetCenter)
    })

    const previewLine = new Konva.Line({
      points: previewPoints.flatMap((p: Point) => [p.x, p.y]),
      stroke: '#4169e1',
      strokeWidth: 2.5,
      closed: true,
      tension: 0.4,
      dash: [4, 4],
      fill: 'rgba(65, 105, 225, 0.3)',
      listening: false,
      name: 'mirror-preview'
    })

    drawingLayer.add(previewLine)
    mirrorPreviewRef.value = previewLine
    showMirrorPreview.value = true // Assuming this controls visibility elsewhere
    drawingLayer.batchDraw()
    console.log('[useMirrorCopy] Preview added to layer:', drawingLayer.name() || drawingLayer.id())
  }

  function hidePreview() {
    if (mirrorPreviewRef.value) {
      const layer = mirrorPreviewRef.value.getLayer()
      mirrorPreviewRef.value.destroy()
      mirrorPreviewRef.value = null
      layer?.batchDraw()
    }
    showMirrorPreview.value = false
  }

  /**
   * 执行镜像拷贝
   */
  function executeMirrorCopy() {
    if (!isMirrorMode.value || targetToothId.value === null) return

    // 获取源牙齿的中心点
    const sourceCenter = getToothCenter(toothId.value)
    const targetCenter = getToothCenter(targetToothId.value)
    if (!sourceCenter || !targetCenter) return

    // 获取源牙齿的控制点
    const sourceControlPoints = getToothControlPoints(toothId.value)
    if (!sourceControlPoints) return

    const mirroredPoints = sourceControlPoints.map((point: Point) => {
      return calculateMirroredPoint(point, sourceCenter, targetCenter)
    })

    const targetSegInfo = targetSegmentInfo.value // Renamed for clarity
    if (!targetSegInfo) return

    const targetSegmentCenterX = targetSegInfo.startX + targetSegInfo.width / 2
    const targetSegmentCenterY = targetSegInfo.topY + targetSegInfo.height / 2

    const relativePoints = mirroredPoints.map((point: Point) => ({
      x: (point.x - targetSegmentCenterX) / (targetSegInfo.width / 2),
      y: (point.y - targetSegmentCenterY) / (targetSegInfo.height / 2)
    }))

    teethStore.updateToothShape(targetToothId.value, {
      points: relativePoints
    })

    ElMessage.success('镜像拷贝成功')
    hidePreview()
    hideArrow()

    const currentToothNode = lineRef.value?.getNode?.()
    const drawingLayer = getDrawingLayer(currentToothNode)
    drawingLayer?.batchDraw()
  }

  function handleMouseEnter() {
    if (isMirrorMode.value) {
      showArrow()
      showPreview()
    }
  }

  function handleMouseLeave() {
    if (isMirrorMode.value) {
      hideArrow()
      hidePreview()
    }
  }

  function handleClick() {
    if (isMirrorMode.value) {
      executeMirrorCopy()
      return true // Indicate event was handled
    }
    return false // Indicate event was not handled
  }

  /**
   * 获取框架的中线X坐标
   * 计算所有牙齿segments的X坐标范围，返回中点
   */
  const getFrameCenterline = () => {
    if (!teethStore.segments || teethStore.segments.length === 0) return null

    let minX = Infinity
    let maxX = -Infinity

    teethStore.segments.forEach((segment: SegmentInfo) => {
      const leftX = segment.startX
      const rightX = segment.startX + segment.width
      minX = Math.min(minX, leftX)
      maxX = Math.max(maxX, rightX)
    })

    return (minX + maxX) / 2
  }

  /**
   * 根据配置计算镜像后的点坐标
   * @param sourcePoint 源点坐标
   * @param sourceCenter 源牙齿中心
   * @param targetCenter 目标牙齿中心
   * @returns 镜像后的点坐标
   */
  const calculateMirroredPoint = (
    sourcePoint: Point,
    sourceCenter: Point,
    targetCenter: Point
  ): Point => {
    let mirroredX: number
    let mirroredY: number

    // 计算X坐标
    if (useFrameCenterlineSymmetry.value) {
      // 使用框架中线对称
      const centerline = getFrameCenterline()
      if (centerline === null) {
        console.warn('[useMirrorCopy] 无法获取框架中线，回退到牙齿中心镜像')
        // 回退到原有逻辑
        const xDistanceFromCenter = sourcePoint.x - sourceCenter.x
        mirroredX = targetCenter.x - xDistanceFromCenter
      } else {
        // 关于框架中线对称：目标点X = 2 * 中线X - 源点X
        mirroredX = 2 * centerline - sourcePoint.x
      }
    } else {
      // 使用原有的牙齿中心镜像逻辑
      const xDistanceFromCenter = sourcePoint.x - sourceCenter.x
      mirroredX = targetCenter.x - xDistanceFromCenter
    }

    // 计算Y坐标
    if (keepVerticalConsistent.value) {
      // 保持Y坐标与源牙齿一致（原有行为）
      mirroredY = sourcePoint.y
    } else {
      // 根据目标牙齿中心调整Y坐标，保持相对位置关系
      const yDistanceFromCenter = sourcePoint.y - sourceCenter.y
      mirroredY = targetCenter.y + yDistanceFromCenter
    }

    return {
      x: mirroredX,
      y: mirroredY
    }
  }

  return {
    isMirrorMode,
    showMirrorPreview,
    targetToothId,
    useFrameCenterlineSymmetry, // 暴露配置变量
    keepVerticalConsistent, // 暴露Y坐标配置变量
    handleMouseEnter,
    handleMouseLeave,
    handleClick
  }
}
