---
description: 
globs: 
alwaysApply: true
---
# Vue 3 + TypeScript 开发规范

## 组件编写规范

- **组件结构**
  ```vue
  <!-- ✅ 推荐的组件结构 -->
  <script setup lang="ts">
  // 1. 类型导入
  import type { PropType } from 'vue'
  
  // 2. 组件导入
  import { ElButton } from 'element-plus'
  
  // 3. Props 定义
  interface Props {
    title: string
    items?: string[]
  }
  
  // 4. Props 声明
  const props = withDefaults(defineProps<Props>(), {
    items: () => []
  })
  
  // 5. Emits 定义和声明
  const emit = defineEmits<{
    (e: 'update', value: string): void
    (e: 'delete'): void
  }>()
  
  // 6. 响应式数据
  const count = ref(0)
  const user = reactive({
    name: '',
    age: 0
  })
  
  // 7. 计算属性
  const doubleCount = computed(() => count.value * 2)
  
  // 8. 方法定义
  const handleClick = () => {
    emit('update', 'new value')
  }
  </script>

  <template>
    <div class="component-container">
      <!-- 使用具有语义的类名 -->
      <h1 class="component-title">{{ title }}</h1>
      
      <!-- 列表渲染使用 key -->
      <ul class="item-list">
        <li 
          v-for="item in items" 
          :key="item"
          class="item"
        >
          {{ item }}
        </li>
      </ul>
      
      <!-- 事件处理使用 handle 前缀 -->
      <el-button @click="handleClick">
        点击次数: {{ count }}
      </el-button>
    </div>
  </template>

  <style lang="less" scoped>
  // 使用 Less 嵌套和变量
  .component {
    &-container {
      padding: var(--spacing-md);
    }
    
    &-title {
      font-size: var(--font-size-lg);
      color: var(--color-primary);
    }
  }
  
  .item {
    &-list {
      margin: var(--spacing-sm) 0;
    }
    
    // 使用 & 符号进行嵌套
    & {
      padding: var(--spacing-xs);
      
      &:hover {
        background-color: var(--color-hover);
      }
    }
  }
  </style>
  ```

## TypeScript 类型定义

- **类型文件组织**
  ```typescript
  // src/types/user.ts
  export interface User {
    id: number
    name: string
    email: string
    role: UserRole
  }
  
  export enum UserRole {
    Admin = 'ADMIN',
    User = 'USER'
  }
  
  // src/types/api.ts
  export interface ApiResponse<T> {
    code: number
    data: T
    message: string
  }
  ```

- **组件 Props 类型**
  ```typescript
  // ✅ 使用 interface 定义 Props
  interface TableProps {
    data: User[]
    loading?: boolean
    pageSize?: number
  }
  
  // ❌ 避免使用 any
  interface BadProps {
    data: any // 应该指定具体类型
    callback: Function // 应该使用具体的函数类型
  }
  ```

## Vue Router 配置

- **路由定义**
  ```typescript
  // src/router/index.ts
  import type { RouteRecordRaw } from 'vue-router'
  
  const routes: RouteRecordRaw[] = [
    {
      path: '/',
      name: 'Home',
      component: () => import('@/views/Home.vue'),
      meta: {
        title: '首页',
        requiresAuth: false
      }
    }
  ]
  ```

## Pinia Store 定义

- **Store 结构**
  ```typescript
  // src/store/user.ts
  import { defineStore } from 'pinia'
  import type { User } from '@/types'
  
  export const useUserStore = defineStore('user', {
    state: () => ({
      currentUser: null as User | null,
      loading: false
    }),
    
    getters: {
      isLoggedIn: (state) => !!state.currentUser
    },
    
    actions: {
      async login(username: string, password: string) {
        this.loading = true
        try {
          // API 调用
        } finally {
          this.loading = false
        }
      }
    }
  })
  ```

## 国际化实现

- **翻译文件组织**
  ```typescript
  // src/locales/zh.ts
  export default {
    module: {
      title: '标题',
      description: '描述'
    }
  }

  // src/locales/en.ts
  export default {
    module: {
      title: 'Title',
      description: 'Description'
    }
  }
  ```

- **使用示例**
  ```typescript
  // 在组件中使用
  const { t } = useI18n()
  const title = t('module.title')
  ```

## 最佳实践

- **组件设计**
  - 使用 `<script setup>` 语法
  - Props 使用运行时声明
  - 避免使用 `any` 类型
  - 组件名使用 PascalCase
  - 事件处理方法使用 `handle` 前缀
  - 组件属性按类型分组排序

- **性能优化**
  - 合理使用 `computed` 缓存计算结果
  - 大型列表使用虚拟滚动
  - 路由组件使用异步加载
  - 避免不必要的响应式数据
  - 使用 `v-once` 处理静态内容
  - 合理使用 `v-show` 和 `v-if`

- **代码组织**
  - 相关的类型定义放在一起
  - 使用类型导入语法 `import type`
  - 保持组件单一职责
  - 抽取复用逻辑到组合式函数
  - 按功能模块组织代码
  - 保持目录结构扁平化

- **样式规范**
  - 使用 BEM 命名规范
  - 利用 Less 嵌套减少重复
  - 使用 CSS 变量管理主题
  - 避免深层嵌套（不超过3层）
  - Scoped CSS 与全局样式分离
  - 组件样式模块化



