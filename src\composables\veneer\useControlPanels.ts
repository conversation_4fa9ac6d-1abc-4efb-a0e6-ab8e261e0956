import { computed } from 'vue'
import { useTeethStore } from '@/store/teeth'
import { useTextureStore } from '@/store/texture'
import { storeToRefs } from 'pinia'
import { useVeneerEditorContext, EditMode } from '@/contexts/VeneerEditorContext'

export function useControlPanels() {
  // 获取牙齿store
  const teethStore = useTeethStore()

  // 获取纹理store
  const textureStore = useTextureStore()
  const { showTexture } = storeToRefs(textureStore)

  // 获取贴面编辑器上下文
  const editorContext = useVeneerEditorContext()

  // 从store中获取可用的模板列表
  const templates = computed(() => teethStore.templates.map((template) => template.id))

  // 从store中获取当前选中的模板
  const currentTemplate = computed({
    get: () => teethStore.currentTemplateId,
    set: (value) => teethStore.setCurrentTemplate(value)
  })

  // 选择模板
  function selectTemplate(template: string) {
    teethStore.setCurrentTemplate(template)
    console.log(`选择了模板: ${template}`)
  }

  // 设置当前变换选项
  function setActiveTransform(option: string) {
    editorContext.setActiveTransform(option as EditMode)
  }

  // 计算属性，获取当前编辑模式的标题
  const transformTitle = computed(() => editorContext.getTransformTitle())

  return {
    // 状态
    showMidline: editorContext.showMidline,
    showTexture, // 使用TextureStore中的showTexture
    showSmileFrame: editorContext.showSmileFrame,
    mirrorEdit: editorContext.mirrorEdit,
    bridgeEdit: editorContext.bridgeEdit,
    activeTransform: editorContext.activeTransform,
    transformTitle,
    templates,
    currentTemplate,

    // 方法
    setActiveTransform,
    selectTemplate
  }
}
