<template>
  <div class="control-panel reset-panel">
    <div class="reset-button">
      <el-button type="primary" plain @click="onResetClick">
        <el-icon><Refresh /></el-icon>
        重设微笑
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Refresh } from '@element-plus/icons-vue'

// Emits
const emit = defineEmits(['reset'])

// 重设微笑
function onResetClick() {
  emit('reset')
}
</script>

<style scoped>
.reset-panel {
  padding: 10px;
  background: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  margin-bottom: 10px;
  background-color: rgba(255, 245, 235, 0.9);
}

.reset-button {
  display: flex;
  justify-content: center;
}

.reset-button .el-button {
  width: 100%;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  background-color: rgba(255, 255, 255, 0.8);
  border-color: #ccc;
  color: #333;
}

.reset-button .el-button:hover {
  background-color: rgba(255, 255, 255, 0.9);
  border-color: #999;
}
</style>
