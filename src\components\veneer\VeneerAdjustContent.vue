<template>
  <div class="veneer-page" :style="{ height: `calc(100vh - ${headerHeight}px)` }">
    <!-- 全屏画布区域 -->
    <SmileCanvas
      v-if="smileImgObj && mouthImgObj"
      ref="canvasRef"
      :showMidline="showMidline"
      :showSmileFrame="showSmileFrame"
      :interactiveTeeth="true"
      :isTexturePage="false"
    />

    <!-- 浮动控制面板 -->
    <div class="floating-controls">
      <!-- 微笑控制面板 -->
      <PhotoControlPanel
        v-model:sliderValue="sliderValue"
        v-model:showSmileFrame="showSmileFrame"
      />

      <!-- 编辑模式面板 -->
      <EditModePanel
        v-model:activeEditMode="activeEditMode"
        :editModeTitle="editModeTitle"
        v-model:mirrorEdit="mirrorEdit"
        v-model:bridgeEdit="bridgeEdit"
      />

      <!-- 微笑数据库面板 -->
      <DatabasePanel :templates="templates" v-model:currentTemplate="currentTemplate" />

      <!-- 重设微笑按钮 -->
      <ResetPanel @reset="handleReset" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { shallowRef, onDeactivated, onActivated } from 'vue'
import { useVeneerEditorContext } from '@/contexts/VeneerEditorContext'
import { useControlPanels } from '@/composables/veneer/useControlPanels'
import { useResetSmile } from '@/composables/veneer/useResetSmile'
import { useWindowResize } from '@/composables/useWindowResize'
import { useTeethStore } from '@/store/teeth'
import { useFrameRelativePositionStore } from '@/store/frameRelativePosition'
import SmileCanvas from '@/components/SmileCanvas.vue'
import PhotoControlPanel from '@/components/veneer/PhotoControlPanel.vue'
import EditModePanel from '@/components/veneer/EditModePanel.vue'
import DatabasePanel from '@/components/veneer/DatabasePanel.vue'
import ResetPanel from '@/components/veneer/ResetPanel.vue'

// 获取贴面编辑器上下文，父组件已经提供了上下文
const editorContext = useVeneerEditorContext()

// 从上下文中获取需要的属性
const headerHeight = editorContext.headerHeight
const smileImgObj = editorContext.smileImgObj
const mouthImgObj = editorContext.mouthImgObj
const sliderValue = editorContext.sliderValue

// 控制面板相关
const {
  showMidline,
  showSmileFrame,
  mirrorEdit,
  bridgeEdit,
  activeEditMode,
  editModeTitle,
  templates,
  currentTemplate
} = useControlPanels()

// 画布引用 - 使用shallowRef减少不必要的深度响应式监听
const canvasRef = shallowRef<InstanceType<typeof SmileCanvas> | null>(null)

// 重置微笑 - 传递上下文
const { handleReset } = useResetSmile(canvasRef, currentTemplate, editorContext)

// 窗口resize监听
useWindowResize(() => {
  if (canvasRef.value && canvasRef.value.canvasContainerRef) {
    // 使用已获取的贴面编辑器上下文
    console.log('window resize')
    editorContext.updateStageSize(canvasRef.value.canvasContainerRef)
  }
})

// 获取store
const teethStore = useTeethStore()
const frameRelativePositionStore = useFrameRelativePositionStore()

// 计算框架相对位置
function calculateFrameRelativePosition() {
  // 获取当前框架
  const currentFrame = teethStore.frame

  // 计算图片的缩放后尺寸
  const imgWidth = editorContext.smileImgNaturalWidth.value * editorContext.imgScale.value
  const imgHeight = editorContext.smileImgNaturalHeight.value * editorContext.imgScale.value

  // 更新框架相对位置
  frameRelativePositionStore.calculateRelativePosition(
    currentFrame.x,
    currentFrame.y,
    currentFrame.width,
    currentFrame.height,
    editorContext.imgCenter.value.x,
    editorContext.imgCenter.value.y,
    imgWidth,
    imgHeight,
    currentFrame.topCurvePoints,
    currentFrame.bottomCurvePoints
  )
}

// 在组件停用前计算框架相对位置
onDeactivated(() => {
  calculateFrameRelativePosition()
})

// 在组件激活时更新牙齿位置
onActivated(() => {
  teethStore.forceUpdateTeethShapes()
})
</script>

<style scoped>
.veneer-page {
  position: relative;
  width: 100%;
  height: 100%;
  background: #f7f8fa;
  overflow: hidden;
}

.floating-controls {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
  width: 280px;
}

:deep(.el-checkbox__inner) {
  border-radius: 3px;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.el-checkbox__label) {
  display: none;
}
</style>
