{"name": "my-vue-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.ts --fix", "format": "prettier --write .", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@types/lodash-es": "^4.17.12", "@types/react": "^19.1.2", "antd": "^5.24.8", "axios": "^1.9.0", "element-plus": "^2.9.8", "inset.js": "^1.0.2", "konva": "^9.3.20", "lodash-es": "^4.17.21", "pinia": "^3.0.2", "react": "^19.1.0", "react-dom": "^19.1.0", "vue": "^3.5.13", "vue-konva": "^3.2.1", "vue-router": "^4.5.0"}, "devDependencies": {"@types/node": "^22.15.2", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "@vitejs/plugin-vue": "^5.2.2", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-vue": "^10.0.0", "jsdom": "^26.1.0", "less": "^4.3.0", "less-loader": "^12.2.0", "msw": "^2.7.5", "prettier": "^3.5.3", "typescript": "~5.7.2", "vite": "^6.3.1", "vitest": "^3.1.2", "vue-tsc": "^2.2.8"}, "msw": {"workerDirectory": ["public"]}}