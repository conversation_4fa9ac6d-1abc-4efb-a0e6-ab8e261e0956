<template>
  <v-group>
    <!-- 牙齿轮廓 - 使用事件处理器对象简化事件绑定 -->
    <v-line
      ref="lineRef"
      :points="outlinePoints"
      :stroke="
        isMirrorMode
          ? showMirrorPreview
            ? '#4169e1'
            : getStrokeColor('#ffffff', '#4169e1', '#4169e1')
          : getStrokeColor('#ffffff', '#ff9f9f', '#ff6b6b')
      "
      :strokeWidth="isMirrorMode && showMirrorPreview ? 3 : 2"
      closed="true"
      :tension="0.4"
      :config="baseConfig"
      @dragstart="handleDragStartWrapper"
      @dragmove="handleDragWrapper"
      @dragend="handleDragEndWrapper"
      @click="handleLineClick"
      @tap="handleLineClick"
      @mousemove="handleLineMouseMove"
      @mouseleave="handleLineMouseLeave"
      @mousedown="handleLineMouseDown"
    />

    <!-- 控制点 - 只在选中且处于精细编辑模式时显示 -->
    <template v-if="selected && isDetailMode">
      <v-circle
        v-for="(point, index) in controlPoints"
        :key="index"
        :x="point.x"
        :y="point.y"
        :radius="hoveredControlPointIndex === index ? 5 : 4"
        :fill="hoveredControlPointIndex === index ? '#ff4747' : '#ff6b6b'"
        stroke="#ffffff"
        :strokeWidth="hoveredControlPointIndex === index ? 2 : 1"
        :config="{ draggable: true }"
        @dragmove="(e: KonvaEventObject<DragEvent>) => handlePointDragWrapper(e, index)"
        @mousedown="(e: KonvaEventObject<MouseEvent>) => handlePointMouseDownWrapper(e, index)"
        @mouseenter="() => handlePointMouseEnter(index)"
        @mouseleave="handlePointMouseLeave"
      />
    </template>

    <!-- 旋转控制手柄 - 只在选中且处于变换模式时显示 -->
    <template v-if="selected && isTransformMode">
      <v-image
        :image="rotateIconObj"
        :x="isRotating ? currentRotateHandlePosition.x : rotateHandlePosition.x"
        :y="isRotating ? currentRotateHandlePosition.y : rotateHandlePosition.y"
        :width="40"
        :height="40"
        :offsetX="20"
        :offsetY="20"
        :scaleX="rotateIconScale"
        :scaleY="rotateIconScale"
        :config="{
          draggable: true
        }"
        @mouseenter="handleRotateMouseEnter"
        @mouseleave="handleRotateMouseLeave"
        @dragstart="handleRotateStartWrapper"
        @dragmove="handleRotateWrapper"
        @dragend="handleRotateEnd"
      />
    </template>
  </v-group>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useTeethStore, type SegmentInfo } from '@/store/teeth'
import type { KonvaEventObject } from 'konva/lib/Node'

import { useVeneerEditorContext } from '@/contexts/VeneerEditorContext'
import { useTooth } from '@/composables/veneer/tooth/useTooth'

// Props
const props = defineProps({
  segmentInfo: {
    type: Object as () => SegmentInfo,
    required: true
  },
  selected: {
    type: Boolean,
    default: false
  },
  listening: {
    type: Boolean,
    default: true
  },
  toothId: {
    type: Number,
    required: true
  }
})

// Store
const teethStore = useTeethStore()

// 获取贴面编辑器上下文
const editorContext = useVeneerEditorContext()

// 线条引用
const lineRef = ref<any>(null)

/**
 * 获取基础配置对象
 * 包含拖拽和点击检测的配置
 */
const baseConfig = computed(() => {
  return {
    draggable: isTransformMode.value && !isEdgeDragging.value && !isMirrorMode.value, // 只在变换模式下且不在边缘拖拽时允许拖动，镜像模式下不允许拖动
    listening: props.listening,
    hitStrokeWidth: 10 // 增加点击检测区域，使曲线更容易被点击
  }
})

// 使用整合后的牙齿 composable
const {
  // 状态
  isDetailMode,
  isTransformMode,
  // isHovered,
  isEdgeDragging,
  isRotating,
  controlPoints,

  // 镜像拷贝相关
  isMirrorMode,
  showMirrorPreview,

  // 形状相关
  outlinePoints,

  // 旋转相关
  rotateIconObj,
  rotateIconScale,
  rotateHandlePosition,
  currentRotateHandlePosition,

  // 控制点 hover 相关
  hoveredControlPointIndex,

  // 事件处理函数
  getStrokeColor,
  handleLineClick,
  handleDragStartWrapper,
  handleDragWrapper,
  handleDragEndWrapper,
  handleRotateStartWrapper,
  handleRotateWrapper,
  handleRotateEnd,
  handleLineMouseMove,
  handleLineMouseDown,
  handleLineMouseLeave,
  handlePointDragWrapper,
  handlePointMouseEnter,
  handlePointMouseLeave,
  handlePointMouseDownWrapper,
  handleRotateMouseEnter,
  handleRotateMouseLeave
} = useTooth(lineRef, props, editorContext, teethStore)
</script>
